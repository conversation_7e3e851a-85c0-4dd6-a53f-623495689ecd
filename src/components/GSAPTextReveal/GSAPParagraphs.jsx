'use client';

import { Children, isValidElement, cloneElement } from 'react';
import GSAPTextReveal from './index';
import { getPreset } from './presets';

/**
 * Composant spécialisé pour animer des textes avec plusieurs paragraphes
 * Préserve l'espacement entre les paragraphes tout en appliquant l'animation GSAP
 * 
 * @param {Object} props - Les propriétés du composant
 * @param {React.ReactNode} props.children - Le contenu JSX avec <br/><br/> ou paragraphes
 * @param {string} props.className - Classes CSS additionnelles
 * @param {string} props.preset - Preset d'animation à utiliser (par défaut 'lines')
 * @param {Object} props.presetOverrides - Surcharges pour le preset
 * @param {number} props.paragraphSpacing - Espacement entre paragraphes en rem (par défaut 1.5)
 */
export default function GSAPParagraphs({
  children,
  className = '',
  preset = 'lines',
  presetOverrides = {},
  paragraphSpacing = 1.5,
  ...props
}) {
  // Fonction pour traiter le contenu et séparer les paragraphes
  const processParagraphs = (content) => {
    if (typeof content === 'string') {
      // Si c'est une string simple, pas de traitement spécial nécessaire
      return [content];
    }

    // Si c'est du JSX, on doit le traiter différemment
    if (isValidElement(content) && content.type === 'React.Fragment' || Array.isArray(content.props?.children)) {
      const children = Array.isArray(content.props?.children) ? content.props.children : [content.props.children];

      // Séparer le contenu par les <br/><br/>
      const paragraphs = [];
      let currentParagraph = [];
      let skipNext = false;

      children.forEach((child, index) => {
        if (skipNext) {
          skipNext = false;
          return;
        }

        if (typeof child === 'string') {
          currentParagraph.push(child);
        } else if (isValidElement(child) && child.type === 'br') {
          // Vérifier si le prochain élément est aussi un <br/>
          const nextChild = children[index + 1];
          if (isValidElement(nextChild) && nextChild.type === 'br') {
            // Double <br/> = nouveau paragraphe
            if (currentParagraph.length > 0) {
              paragraphs.push(currentParagraph.join('').trim());
              currentParagraph = [];
            }
            // Marquer pour ignorer le prochain <br/>
            skipNext = true;
          } else {
            // Simple <br/> = saut de ligne dans le même paragraphe
            currentParagraph.push('\n');
          }
        } else if (child !== null) {
          currentParagraph.push(child);
        }
      });

      // Ajouter le dernier paragraphe s'il existe
      if (currentParagraph.length > 0) {
        paragraphs.push(currentParagraph.join('').trim());
      }

      return paragraphs.filter(p => p.length > 0);
    }

    // Fallback : traiter comme un seul paragraphe
    return [content];
  };

  const paragraphs = processParagraphs(children);

  return (
    <div className={className} {...props}>
      {paragraphs.map((paragraph, index) => {
        // Calculer le délai pour que chaque paragraphe commence après la fin du précédent
        // Durée approximative pour un paragraphe complet : 2 secondes (durée + stagger + marge)
        const paragraphDuration = 2;
        const baseDelay = presetOverrides.delay || 0.8;
        const sequentialDelay = index * paragraphDuration;

        return (
          <div key={index}>
            <GSAPTextReveal
              as="p"
              {...getPreset(preset, {
                delay: baseDelay + sequentialDelay,
                ...presetOverrides
              })}
            >
              {paragraph}
            </GSAPTextReveal>
            {index < paragraphs.length - 1 && (
              <div style={{ height: `${paragraphSpacing}rem` }} />
            )}
          </div>
        );
      })}
    </div>
  );
}
